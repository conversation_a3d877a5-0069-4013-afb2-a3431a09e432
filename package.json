{"name": "audiorecorderapp", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "expo": "~54.0.6", "expo-audio": "~1.0.11", "expo-av": "~16.0.7", "expo-constants": "~18.0.8", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-modules-core": "~3.0.15", "expo-permissions": "^14.4.0", "expo-sharing": "~14.0.7", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.7", "lottie-ios": "^4.5.1", "lottie-react-native": "~7.3.1", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-web": "~0.21.0"}, "devDependencies": {"@types/react": "~19.1.0", "eslint": "^9.25.0", "eslint-config-expo": "~10.0.0", "typescript": "~5.9.2"}, "private": true}