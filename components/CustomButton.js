﻿import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

export default function CustomButton({ title, onPress, disabled, color }) {
    return (
        <TouchableOpacity
            onPress={onPress}
            disabled={disabled}
            activeOpacity={0.7}
            style={[
                styles.button,
                {
                    backgroundColor: disabled ? "#D1D1D6" : color || "#007AFF", // iOS blue fallback
                    shadowOpacity: disabled ? 0 : 0.15,
                },
            ]}
        >
            <View>
                <Text style={styles.text}>{title}</Text>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        paddingVertical: 14,
        paddingHorizontal: 20,
        borderRadius: 14, // ✅ smoother round like iOS/Pixel
        marginVertical: 8,
        alignItems: "center",
        width: "85%",
        alignSelf: "center",

        // ✅ Shadow for iOS
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 6,

        // ✅ Elevation for Android
        elevation: 3,
    },
    text: {
        color: "#fff",
        fontSize: 17,
        fontWeight: "600",
        textAlign: "center",
        letterSpacing: 0.5,
    },
});
