import React, { useState, useRef, useEffect, useMemo } from "react";
import {
    View,
    Text,
    Alert,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Animated,
    Share,
} from "react-native";
import { Audio } from "expo-av";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { Swipeable } from "react-native-gesture-handler";

// ✅ Safe fallback for LinearGradient
let LinearGradient;
try {
    LinearGradient = require("expo-linear-gradient").LinearGradient;
} catch (e) {
    console.warn("expo-linear-gradient not installed → using View fallback");
    LinearGradient = ({ children, style }) => (
        <View style={[{ backgroundColor: "#1e1e2e" }, style]}>{children}</View>
    );
}

export default function Recorder() {
    const [recording, setRecording] = useState(null);
    const [isPaused, setIsPaused] = useState(false);
    const [isBusy, setIsBusy] = useState(false);
    const [seconds, setSeconds] = useState(0);
    const [recordings, setRecordings] = useState([]);
    const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
    const [isPlayingPaused, setIsPlayingPaused] = useState(false);

    const timerRef = useRef(null);
    const soundRef = useRef(null);

    // 🔴 Animations
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const glowAnim = useRef(new Animated.Value(0.5)).current;
    const waveAnims = useMemo(
        () => Array.from({ length: 6 }, () => new Animated.Value(0.4)),
        []
    );

    useEffect(() => {
        return () => {
            clearInterval(timerRef.current);
            if (soundRef.current) {
                soundRef.current.unloadAsync();
            }
        };
    }, []);

    function startTimer(reset = false) {
        if (reset) setSeconds(0);
        clearInterval(timerRef.current);
        timerRef.current = setInterval(() => setSeconds((s) => s + 1), 1000);

        Animated.loop(
            Animated.sequence([
                Animated.timing(pulseAnim, { toValue: 1.2, duration: 600, useNativeDriver: true }),
                Animated.timing(pulseAnim, { toValue: 1, duration: 600, useNativeDriver: true }),
            ])
        ).start();

        Animated.loop(
            Animated.sequence([
                Animated.timing(glowAnim, { toValue: 1, duration: 800, useNativeDriver: false }),
                Animated.timing(glowAnim, { toValue: 0.3, duration: 800, useNativeDriver: false }),
            ])
        ).start();

        waveAnims.forEach((anim, i) => {
            Animated.loop(
                Animated.sequence([
                    Animated.timing(anim, { toValue: 1, duration: 400 + i * 100, useNativeDriver: true }),
                    Animated.timing(anim, { toValue: 0.3, duration: 400 + i * 120, useNativeDriver: true }),
                ])
            ).start();
        });
    }

    function stopTimer() {
        clearInterval(timerRef.current);
        timerRef.current = null;
        pulseAnim.setValue(1);
        glowAnim.setValue(0.5);
        waveAnims.forEach((anim) => anim.stopAnimation(() => anim.setValue(0.4)));
    }

    async function startRecording() {
        try {
            if (isBusy) return;
            setIsBusy(true);
            await Haptics.selectionAsync();

            const permission = await Audio.requestPermissionsAsync();
            if (permission.status !== "granted") {
                Alert.alert("Permission needed", "Microphone access required");
                setIsBusy(false);
                return;
            }

            await Audio.setAudioModeAsync({
                allowsRecordingIOS: true,
                playsInSilentModeIOS: true,
                staysActiveInBackground: true,
                shouldDuckAndroid: true,
            });

            if (recording) {
                try {
                    await recording.stopAndUnloadAsync();
                } catch { }
                setRecording(null);
            }

            const rec = new Audio.Recording();
            await rec.prepareToRecordAsync(Audio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY);
            await rec.startAsync();

            setRecording(rec);
            setIsPaused(false);
            startTimer(true);
        } catch (err) {
            console.error("Error starting recording", err);
        } finally {
            setIsBusy(false);
        }
    }

    async function togglePauseResume() {
        if (!recording) return;
        await Haptics.selectionAsync();
        try {
            if (isPaused) {
                await recording.startAsync();
                setIsPaused(false);
                startTimer(false);
            } else {
                await recording.pauseAsync();
                setIsPaused(true);
                stopTimer();
            }
        } catch (err) {
            console.error("Pause/Resume failed", err);
        }
    }

    async function stopRecording() {
        if (!recording) return;
        setIsBusy(true);
        await Haptics.selectionAsync();
        try {
            await recording.stopAndUnloadAsync();
            const fileUri = recording.getURI();
            setRecordings((prev) => [...prev, { uri: fileUri, name: `Recording ${prev.length + 1}` }]);
            setRecording(null);
            setIsPaused(false);
            stopTimer();
        } catch (err) {
            console.error("stopRecording error", err);
        } finally {
            setIsBusy(false);
        }
    }

    async function playRecording(uri, index) {
        try {
            if (soundRef.current && currentlyPlaying === index) {
                const status = await soundRef.current.getStatusAsync();
                if (status.isLoaded) {
                    if (status.isPlaying) {
                        await soundRef.current.pauseAsync();
                        setIsPlayingPaused(true);
                    } else {
                        await soundRef.current.playAsync();
                        setIsPlayingPaused(false);
                    }
                }
                return;
            }

            if (soundRef.current) {
                const status = await soundRef.current.getStatusAsync();
                if (status.isLoaded) {
                    await soundRef.current.stopAsync();
                    await soundRef.current.unloadAsync();
                }
            }

            const { sound } = await Audio.Sound.createAsync({ uri });
            soundRef.current = sound;
            setCurrentlyPlaying(index);
            setIsPlayingPaused(false);
            await sound.playAsync();
            sound.setOnPlaybackStatusUpdate((status) => {
                if (status.didJustFinish) {
                    setCurrentlyPlaying(null);
                    setIsPlayingPaused(false);
                }
            });
        } catch (err) {
            console.error("playRecording error", err);
        }
    }

    async function stopPlayback() {
        if (soundRef.current) {
            const status = await soundRef.current.getStatusAsync();
            if (status.isLoaded) {
                await soundRef.current.stopAsync();
                await soundRef.current.unloadAsync();
            }
            soundRef.current = null;
            setCurrentlyPlaying(null);
            setIsPlayingPaused(false);
        }
    }

    async function deleteRecording(index) {
        if (currentlyPlaying === index && soundRef.current) {
            await stopPlayback();
        }
        setRecordings((prev) => prev.filter((_, i) => i !== index));
    }

    async function shareRecording(uri) {
        try {
            await Share.share({ url: uri, message: "Check out my recording 🎙" });
        } catch (err) {
            console.error("share error", err);
        }
    }

    // Swipe actions
    const renderRightActions = (index) => (
        <TouchableOpacity
            style={styles.swipeDelete}
            onPress={() => deleteRecording(index)}
        >
            <Ionicons name="trash-outline" size={28} color="#fff" />
            <Text style={styles.swipeDeleteText}>Delete</Text>
        </TouchableOpacity>
    );

    const renderItem = ({ item, index }) => (
        <Swipeable renderRightActions={() => renderRightActions(index)}>
            <View style={styles.recordCard}>
                <View style={styles.recordTop}>
                    <Ionicons name="musical-notes" size={22} color="#38bdf8" />
                    <Text style={styles.recordName}>{item.name}</Text>
                </View>
                <View style={styles.divider} />
                <View style={styles.actions}>
                    <TouchableOpacity onPress={() => playRecording(item.uri, index)}>
                        <Ionicons
                            name={
                                currentlyPlaying === index
                                    ? isPlayingPaused
                                        ? "play-circle"
                                        : "pause-circle"
                                    : "play-circle-outline"
                            }
                            size={32}
                            color="#38bdf8"
                        />
                    </TouchableOpacity>
                    {currentlyPlaying === index && (
                        <TouchableOpacity onPress={stopPlayback}>
                            <Ionicons name="stop-circle" size={32} color="#f59e0b" />
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity onPress={() => shareRecording(item.uri)}>
                        <Ionicons name="share-outline" size={28} color="#22c55e" />
                    </TouchableOpacity>
                    {/* ✅ Delete button inside card */}
                    <TouchableOpacity onPress={() => deleteRecording(index)}>
                        <Ionicons name="trash-outline" size={28} color="#EF4444" />
                    </TouchableOpacity>
                </View>
            </View>
            {/* deck line */}
            <View style={styles.deckLine} />
        </Swipeable>
    );

    return (
        <LinearGradient colors={["#0f172a", "#1e293b", "#334155"]} style={{ flex: 1 }}>
            <View style={styles.container}>
                <Text style={styles.title}>🎙 Suvan Audio Recorder</Text>

                {recording && (
                    <View style={styles.timerRow}>
                        <Animated.View style={[styles.pulseDot, { transform: [{ scale: pulseAnim }] }]} />
                        <Text style={styles.timer}>{seconds}s</Text>
                        <View style={styles.waveform}>
                            {waveAnims.map((anim, i) => (
                                <Animated.View
                                    key={i}
                                    style={[styles.waveBar, { transform: [{ scaleY: anim }] }]}
                                />
                            ))}
                        </View>
                    </View>
                )}

                <View style={styles.controlsRow}>
                    {!recording ? (
                        <TouchableOpacity onPress={startRecording}>
                            <Ionicons name="mic-circle" size={90} color="#34C759" />
                        </TouchableOpacity>
                    ) : (
                        <>
                            <TouchableOpacity onPress={togglePauseResume}>
                                <Ionicons
                                    name={isPaused ? "play-circle" : "pause-circle"}
                                    size={90}
                                    color="#FACC15"
                                />
                            </TouchableOpacity>
                            <TouchableOpacity onPress={stopRecording}>
                                <Ionicons name="stop-circle" size={90} color="#EF4444" />
                            </TouchableOpacity>
                        </>
                    )}
                </View>

                <Text style={styles.listTitle}>Saved Recordings</Text>
                {recordings.length === 0 ? (
                    <Text style={styles.noRecordings}>No recordings yet</Text>
                ) : (
                    <FlatList
                        data={recordings}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={renderItem}
                    />
                )}
            </View>
        </LinearGradient>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1, padding: 20 },
    title: { fontSize: 24, fontWeight: "700", textAlign: "center", marginBottom: 20, color: "#fff" },
    timerRow: { flexDirection: "row", alignItems: "center", justifyContent: "center", marginBottom: 20 },
    pulseDot: { width: 14, height: 14, borderRadius: 7, backgroundColor: "#FF3B30", marginRight: 8 },
    timer: { fontSize: 28, fontWeight: "bold", color: "#fff" },
    waveform: { flexDirection: "row", marginLeft: 12 },
    waveBar: { width: 4, height: 18, backgroundColor: "#fff", borderRadius: 2, marginHorizontal: 2 },
    controlsRow: { flexDirection: "row", justifyContent: "center", gap: 30, marginVertical: 20 },
    listTitle: { fontSize: 18, fontWeight: "600", marginTop: 20, marginBottom: 10, color: "#fff" },
    noRecordings: { fontSize: 14, color: "#ddd", textAlign: "center" },
    recordCard: {
        backgroundColor: "#1e293b",
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 16,
        marginVertical: 4,
    },
    recordTop: { flexDirection: "row", alignItems: "center", marginBottom: 10 },
    recordName: { fontSize: 16, fontWeight: "500", marginLeft: 10, color: "#fff" },
    divider: { height: 1, backgroundColor: "#475569", marginVertical: 8 },
    actions: { flexDirection: "row", justifyContent: "space-around", alignItems: "center", marginTop: 6 },
    deckLine: { height: 2, backgroundColor: "#334155", marginHorizontal: 16, borderRadius: 2 },
    swipeDelete: {
        backgroundColor: "#EF4444",
        justifyContent: "center",
        alignItems: "center",
        width: 100,
        borderRadius: 16,
        marginVertical: 4,
    },
    swipeDeleteText: { color: "#fff", fontSize: 14, marginTop: 4 },
});

