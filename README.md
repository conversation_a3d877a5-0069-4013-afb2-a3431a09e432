# 🎙️ Suvan Audio Recorder

A cross-platform mobile audio recorder app built with **React Native + Expo** as part of the Mobile App Developer assignment.

---

## 🚀 Features
- 🎤 **Record Audio** using device microphone
- ⏸️ **Pause & Resume** while recording
- ⏹️ **Stop & Save** recordings locally
- ▶️ **Playback controls** (Play, Pause, Resume, Stop)
- 📱 **Background support** (recording continues if app is backgrounded)
- 📞 **Smart interruption handling** (auto-pause when interrupted, resume after)
- 🗑️ **Delete recordings** (via swipe-to-delete **and** delete button)
- 📤 **Share recordings** with system share sheet
- 🎨 **Enhanced UX**:
  - Animated waveform & glowing pulse while recording
  - Modern gradient backgrounds
  - Haptic feedback on key actions
  - Card-based saved recordings list with icons

---

## 🛠️ Tech Stack
- **React Native (Expo)**
- **expo-av** → audio recording & playback
- **expo-linear-gradient** → UI gradients
- **expo-haptics** → button haptics
- **react-native-gesture-handler** → swipe-to-delete
- **expo-sharing** → share recordings

---

## ⚙️ Installation & Setup
1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/suavn-audio-recorder.git
   cd suavn-audio-recorder
